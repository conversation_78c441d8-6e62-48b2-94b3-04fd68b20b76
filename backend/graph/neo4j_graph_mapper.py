from typing import List

from yunfu.common import LogUtils
from yunfu.db.graph.core.models.config import Config
from yunfu.db.graph.graph.graph_dbs import Neo4jGraphDb
from yunfu.db.graph.models import Edge, Node

from backend.utils import TimeUtils

from .base_graph_mapper import BaseGraphMapper

logger = LogUtils.get_logger(__name__)


class Neo4jGraphMapper(BaseGraphMapper):
    graph_db: Neo4jGraphDb

    def __init__(self, db_config: dict, enable_version: bool = True):
        self.graph_db = Neo4jGraphDb(
            Config(db=db_config, version={"enabled": enable_version})  # type: ignore
        )

    def _pre_process_node(self, node: Node) -> None:
        """预处理节点"""
        now_time = TimeUtils.now_str()
        node.props["_create_time"] = now_time
        node.props["_update_time"] = now_time

    def insert_node(self, space: str, node: Node) -> Node:
        """插入点"""
        graph = self.graph_db.get_graph(space)
        self._pre_process_node(node)
        return graph.insert_node(node)

    def _pre_process_edge(self, edge: Edge) -> None:
        """预处理边"""
        now_time = TimeUtils.now_str()
        edge.props["_create_time"] = now_time
        edge.props["_update_time"] = now_time

    def insert_edge(self, space: str, edge: Edge) -> Edge:
        """插入边"""
        graph = self.graph_db.get_graph(space)
        self._pre_process_edge(edge)
        return graph.insert_edge(edge)

    def get_all_relations_by_node(self, space: str, node: Node) -> List[Edge]:
        graph = self.graph_db.get_graph(space)
        return graph.edges.match(
            src_node_props=[("_eid", "=", node.props["_eid"])]
        ).all()

    def export_entities(
        self,
        space: str,
        skip: int = 0,
        limit: int = 20,
        version: str = "c",
        export_type: str = "kg",
    ) -> List[Node]:
        """导出实体"""
        graph = self.graph_db.get_graph(space)
        if version != "c":
            graph = graph.get_history_graph(version)  # type: ignore
        if export_type == "ontology":
            return graph.nodes.match(types=["concept"]).limit(limit).skip(skip).all()  # type: ignore
        if export_type == "entity":
            return (  # type: ignore
                graph.nodes.match()
                .where(where_query="NOT n:concept")
                .limit(limit)
                .skip(skip)
                .all()
            )
        return graph.nodes.match().limit(limit).skip(skip).all()  # type: ignore

    def export_relation_triples(
        self, space: str, skip: int, limit: int, version: str, export_type: str = "kg"
    ) -> dict:
        """导出关系三元组"""
        relations = self.export_relations(space, skip, limit, version, export_type)
        data = []
        for relation in relations:
            data.append(
                [
                    relation.src_node.name,  # type: ignore
                    relation.props["name"],
                    relation.dst_node.name,  # type: ignore
                ]
            )
        total = len(data)
        return {"total": total, "data": data}

    def export_relations(
        self,
        space: str,
        skip: int = 0,
        limit: int = 20,
        version: str = "c",
        export_type: str = "kg",
    ) -> List[Edge]:
        """导出关系"""
        graph = self.graph_db.get_graph(space)
        if version != "c":
            graph = graph.get_history_graph(version)  # type: ignore
        if export_type == "ontology":
            return (  # type: ignore
                graph.edges.match(
                    src_node_types={"concept"}, dst_node_types={"concept"}
                )
                .limit(limit)
                .skip(skip)
                .all()
            )
        if export_type == "entity":
            return (  # type: ignore
                graph.edges.match()
                .where(where_query="NOT n:concept AND NOT m:concept")
                .limit(limit)
                .skip(skip)
                .all()
            )
        return graph.edges.match().limit(limit).skip(skip).all()  # type: ignore

    def delete_node_version_by_eid(self, space: str, eid: str, version: str):
        """删除节点版本"""
        graph = self.graph_db.get_graph(space)
        query = f"""MATCH (n:{space}) WHERE n._eid='{eid}' REMOVE n:{version}"""
        graph.client.run(query)

    def delete_relation_version_by_id(self, space: str, id: str, version: str) -> None:
        """删除边版本"""
        # TODO: 版本
        graph = self.graph_db.get_graph(space)
        graph.delete_edge(id)

    def partial_update_node(self, space: str, node_id: str, props: dict) -> Node:
        graph = self.graph_db.get_graph(space)
        now_time = TimeUtils.now_str()
        node = graph.get_node(node_id)
        create_time = node.props.get("_create_time")
        props.update(
            {
                "_create_time": create_time or now_time,
                "_update_time": now_time,
            }
        )
        return graph.update_node(node_id, props)

    def delete_node_by_name(self, space: str, types: List[str], name: str):
        graph = self.graph_db.get_graph(space)
        query = f"MATCH (n:{':'.join(types)}) WHERE n.name='{name}' REMOVE n:c"
        graph.client.run(query)

    def delete_node_by_ontology(self, space: str, types: List[str], name: str):
        graph = self.graph_db.get_graph(space)
        query = (
            f"MATCH (n)-[r]->(m:{':'.join(types)}) WHERE n.name='{name}'"
            " AND r.name='属于 AND NOT n:concept REMOVE n:c"
        )
        graph.client.run(query)

    def get_edges_with_eid(self, space: str, labels: List[str], eid: str):
        graph = self.graph_db.get_graph(space)
        return graph.edges.match(
            src_node_types=labels,
            src_node_props=[("_eid", "=", eid)],
        ).all()

    def partial_update_edge(self, space: str, edge_id: str, props: dict) -> Edge:
        graph = self.graph_db.get_graph(space)
        now_time = TimeUtils.now_str()
        edge = graph.get_edge(edge_id)
        create_time = edge.props.get("_create_time")
        props.update(
            {
                "_create_time": create_time or now_time,
                "_update_time": now_time,
            }
        )
        return graph.update_edge(edge_id, props)

    def create_edge(self, edge: Edge) -> Edge:
        """创建边（兼容旧接口）"""
        # 从edge对象中提取space信息，如果没有则使用默认值
        space = getattr(edge, 'space', 'default')
        return self.insert_edge(space, edge)

    def save_node(self, node: Node) -> Node:
        """保存节点（兼容旧接口）"""
        # 从node对象中提取space信息，如果没有则使用默认值
        space = getattr(node, 'space', 'default')
        return self.insert_node(space, node)

    def sync_entity_type_by_ontology_name(self, space: str, kg_id: int, ontology_labels: list, old_node_name: str, new_property_value: str) -> None:
        """根据本体名称同步实体的_type属性并移除旧标签"""
        graph = self.graph_db.get_graph(space)
        entity_labels = ["c", f"KG{kg_id}"]
        query = (
            f'MATCH (m:{":".join(entity_labels)})-[r]->(n:{":".join(ontology_labels)}) '
            f'WHERE r.name="属于" AND n.name="{new_property_value}" '
            f'SET m._type="{new_property_value}" REMOVE m:{old_node_name}'
        )
        graph.client.run(query)

    def sync_entity_property_rename(self, space: str, ontology_labels: list, node_name: str, old_property_name: str, new_property_name: str) -> None:
        """同步实体属性重命名"""
        graph = self.graph_db.get_graph(space)
        query = (
            f'MATCH (m)-[r]->(n:{":".join(ontology_labels)}) '
            f'WHERE r.name="属于" AND n.name="{node_name}" '
            f'SET m.{new_property_name}=m.{old_property_name} REMOVE m.{old_property_name}'
        )
        graph.client.run(query)

    def sync_entity_property_remove(self, space: str, ontology_labels: list, node_name: str, property_name: str) -> None:
        """同步删除实体属性"""
        graph = self.graph_db.get_graph(space)
        query = (
            f'MATCH (m)-[r]->(n:{":".join(ontology_labels)}) '
            f'WHERE r.name="属于" AND n.name="{node_name}" '
            f'REMOVE m.{property_name}'
        )
        graph.client.run(query)

    def sync_entity_relation_rename(self, space: str, kg_id: int, src_type: str, dst_type: str, old_relation_name: str, new_relation_name: str) -> None:
        """同步实体关系重命名"""
        graph = self.graph_db.get_graph(space)
        entity_labels = ["c", f"KG{kg_id}"]
        query = (
            f'MATCH (m:{":".join(entity_labels)})-[r]->(n:{":".join(entity_labels)}) '
            f'WHERE m._type="{src_type}" AND r.name="{old_relation_name}" AND n._type="{dst_type}" '
            f'SET r.name="{new_relation_name}"'
        )
        graph.client.run(query)

    def sync_entity_relation_remove(self, space: str, kg_id: int, src_type: str, dst_type: str, relation_name: str) -> None:
        """同步删除实体关系（设置c=null）"""
        graph = self.graph_db.get_graph(space)
        entity_labels = ["c", f"KG{kg_id}"]
        query = (
            f'MATCH (m:{":".join(entity_labels)})-[r]->(n:{":".join(entity_labels)}) '
            f'WHERE m._type="{src_type}" AND r.name="{relation_name}" AND n._type="{dst_type}" '
            f'SET r.c=null'
        )
        graph.client.run(query)
